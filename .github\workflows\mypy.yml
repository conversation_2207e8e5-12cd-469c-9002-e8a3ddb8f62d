name: "mypy – static type checks"

on:
  pull_request:
    paths: ["**/*.py"]
  push:
    branches: [main]
    paths: ["**/*.py"]

jobs:
  mypy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      # ---------- pip wheel cache ----------
      - name: <PERSON><PERSON> pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-py${{ matrix.python-version }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-py${{ matrix.python-version }}-pip-

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install deps + mypy
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      # ---------- mypy incremental cache ----------
      - name: <PERSON><PERSON> mypy .mypy_cache
        uses: actions/cache@v4
        with:
          path: .mypy_cache
          key: ${{ runner.os }}-mypy-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-mypy-

      - name: Type‑check
        run: |
          mypy src
