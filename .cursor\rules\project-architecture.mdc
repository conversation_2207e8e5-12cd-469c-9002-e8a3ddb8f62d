---
alwaysApply: true
---

# RepoAudit Project Architecture

## Overview
RepoAudit is a multi-agent LLM framework for repository-level code auditing that detects bugs (NPD, Memory Leak, Use After Free) across multiple languages (C/C++, Java, Python, Go) without compilation.

## Entry Point
The main entry point is [repoaudit.py](mdc:src/repoaudit.py), which:
- Initializes language-specific TSAnalyzer based on `--language` argument
- Launches the specified agent (`metascan` or `dfbscan`) based on `--scan-type`
- Traverses project files and loads them into memory

## Core Architecture Components

### 1. Agents ([src/agent/](mdc:src/agent/))
Multi-agent system where each agent targets specific auditing tasks:
- **Base**: [agent.py](mdc:src/agent/agent.py) - Base `Agent` class
- **MetaScanAgent** ([metascan.py](mdc:src/agent/metascan.py)): Wraps parsing-based analyzer for syntactic analysis
- **DFBScanAgent** ([dfbscan.py](mdc:src/agent/dfbscan.py)): Inter-procedural data-flow bug detection

### 2. TSAnalyzer - Parsing Layer ([src/tstool/analyzer/](mdc:src/tstool/analyzer/))
Tree-sitter based AST parsing and analysis:
- **Base**: [TS_analyzer.py](mdc:src/tstool/analyzer/TS_analyzer.py) - Abstract base class
- **Language-specific**: [Cpp_TS_analyzer.py](mdc:src/tstool/analyzer/Cpp_TS_analyzer.py), [Go_TS_analyzer.py](mdc:src/tstool/analyzer/Go_TS_analyzer.py), [Java_TS_analyzer.py](mdc:src/tstool/analyzer/Java_TS_analyzer.py), [Python_TS_analyzer.py](mdc:src/tstool/analyzer/Python_TS_analyzer.py)

Extracts: function constructs, call graphs, control-flow, CFL-reachability

### 3. TSTool - Parsing-based Tools ([src/tstool/dfbscan_extractor/](mdc:src/tstool/dfbscan_extractor/))
Language/bug-specific extractors (e.g., NULL value extraction):
- Structure: `{Language}/{Language}_{BugType}_extractor.py`
- Example: [Cpp_NPD_extractor.py](mdc:src/tstool/dfbscan_extractor/Cpp/Cpp_NPD_extractor.py)

### 4. LLMTool - Neural Analysis Layer ([src/llmtool/](mdc:src/llmtool/))
LLM-powered semantic analysis tools:
- **Base**: [LLM_tool.py](mdc:src/llmtool/LLM_tool.py) - Base `LLMTool` class with `LLMToolInput`/`LLMToolOutput`
- **Utils**: [LLM_utils.py](mdc:src/llmtool/LLM_utils.py) - LLM invocation utilities

For DFBScan ([src/llmtool/dfbscan/](mdc:src/llmtool/dfbscan/)):
- [intra_dataflow_analyzer.py](mdc:src/llmtool/dfbscan/intra_dataflow_analyzer.py) - Intra-procedural data-flow facts
- [path_validator.py](mdc:src/llmtool/dfbscan/path_validator.py) - Path feasibility validation

### 5. Memory System ([src/memory/](mdc:src/memory/))
Three-tier memory architecture:

**Syntactic Memory** ([src/memory/syntactic/](mdc:src/memory/syntactic/)): AST-level constructs
- [value.py](mdc:src/memory/syntactic/value.py) - Program values (params, args, returns)
- [function.py](mdc:src/memory/syntactic/function.py) - Function metadata
- [api.py](mdc:src/memory/syntactic/api.py) - API call information

**Semantic Memory** ([src/memory/semantic/](mdc:src/memory/semantic/)): Agent states
- [state.py](mdc:src/memory/semantic/state.py) - Base `State` class
- [dfbscan_state.py](mdc:src/memory/semantic/dfbscan_state.py) - Data-flow facts and analysis state
- [metascan_state.py](mdc:src/memory/semantic/metascan_state.py) - Syntactic scan state

**Report Memory** ([src/memory/report/](mdc:src/memory/report/)): Final results
- [bug_report.py](mdc:src/memory/report/bug_report.py) - Bug/debug reports

### 6. Prompts ([src/prompt/](mdc:src/prompt/))
LLM prompt templates organized by language and agent:
- Structure: `{Language}/{agent_name}/{tool_name}.json`
- Example: [Python/dfbscan/intra_dataflow_analyzer.json](mdc:src/prompt/Python/dfbscan/intra_dataflow_analyzer.json)

### 7. UI Layer ([src/ui/](mdc:src/ui/))
- [web_ui.py](mdc:src/ui/web_ui.py) - Web interface
- [logger.py](mdc:src/ui/logger.py) - Logging utilities

## Data Flow Pipeline
```
Code → TSAnalyzer → Agent (uses TSTool + LLMTool) → Reports
         ↓              ↓                              ↓
    Syntactic     Semantic Memory              Report Memory
     Memory
```

## Supported Bug Types
- **C/C++**: NPD, Memory Leak (MLK), Use After Free (UAF)
- **Java/Python/Go**: NPD (Null Pointer Dereference)

Default checkers defined in [repoaudit.py](mdc:src/repoaudit.py) line 16-21.

## Key Directories
- [benchmark/](mdc:benchmark/) - Test cases organized by language and bug type
- [docs/](mdc:docs/) - Architecture, extension guides
- [lib/](mdc:lib/) - Tree-sitter language bindings (built via build.py)
 