@echo off
echo ==========================================
echo RepoAudit 功能测试总结
echo ==========================================
echo.

echo [检查 1] 容器状态
docker ps | findstr repoaudit
echo.

echo [检查 2] Ollama 连接
docker exec repoaudit-app curl -s http://host.docker.internal:11434/api/tags | findstr deepseek
echo.

echo [检查 3] Web UI 状态
docker exec repoaudit-app curl -s http://localhost:8501 | findstr Streamlit
echo.

echo [检查 4] 结果目录结构
docker exec repoaudit-app ls -lah /app/result/dfbscan/
echo.

echo [检查 5] 运行快速测试（qwen2.5-coder:7b - 更大模型）
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name qwen2.5-coder:7b --project-path /app/benchmark/Java/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0 --is-reachable"
echo.

echo [检查 6] 查看新结果
docker exec repoaudit-app find /app/result -name "*.json" -type f
echo.

echo ==========================================
echo 测试完成！
echo ==========================================
echo.
echo Web UI: http://localhost:8501
echo.
start http://localhost:8501
pause 