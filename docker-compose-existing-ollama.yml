version: '3.8'

services:
  # RepoAudit 服务 - 连接到已存在的 Ollama
  repoaudit:
    build: .
    container_name: repoaudit-app
    ports:
      - "8501:8501"
    volumes:
      - ./benchmark:/app/benchmark
      - ./result:/app/result
      - ./src:/app/src
    environment:
      # 连接到宿主机的 Ollama（如果 Ollama 在宿主机运行）
      - OLLAMA_HOST=http://host.docker.internal:11434
      # 或者连接到 Docker 网络中的 Ollama 容器
      # - OLLAMA_HOST=http://ollama:11434
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - repoaudit-net

networks:
  repoaudit-net:
    driver: bridge 