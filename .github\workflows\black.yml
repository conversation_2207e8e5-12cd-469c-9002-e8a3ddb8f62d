name: "Black – code-style check"

on:
  pull_request:
    paths: ["**/*.py"]
  push:
    branches: [main]
    paths: ["**/*.py"]

jobs:
  black:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      # ---------- pip wheel cache ----------
      - name: <PERSON><PERSON> pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-black-pip-${{ hashFiles('pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-black-pip-

      # ---------- Black cache (formatting state) ----------
      - name: Cache Black .cache
        uses: actions/cache@v4
        with:
          path: .cache/black
          key: ${{ runner.os }}-black-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-black-

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install <PERSON> (pinned)
        run: |
          python -m pip install --upgrade pip
          pip install black==24.10.0

      - name: Run <PERSON> in check mode
        run: black --check --diff src
