# RepoAudit + Ollama 部署

## 快速开始

1. 运行部署脚本：
```cmd
build.bat
```

2. 访问 Web UI：
```
http://localhost:8501
```

## 手动命令

```cmd
# 构建镜像
docker build -t repoaudit:latest .

# 启动容器
docker-compose -f docker-compose-existing-ollama.yml up -d

# 运行检测
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name deepseek-coder:1.3b --project-path /app/benchmark/Java/toy --bug-type NPD --scan-type dfbscan --call-depth 2 --max-neural-workers 1"

# 查看结果
docker exec repoaudit-app find /app/result -name "detect_info.json"
```

## 支持的模型

- deepseek-coder:1.3b
- codegemma:2b  
- qwen2.5-coder:7b

## 文件说明

- `build.bat` - 一键部署脚本
- `Dockerfile` - Docker 镜像配置
- `docker-compose-existing-ollama.yml` - 连接已有 Ollama 的配置
- `src/llmtool/LLM_utils.py` - Ollama 集成代码
- `src/ui/web_ui.py` - Web 界面 