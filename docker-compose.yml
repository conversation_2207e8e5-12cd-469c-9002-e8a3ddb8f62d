version: '3.8'

services:
  # Ollama 服务
  ollama:
    image: ollama/ollama:latest
    container_name: repoaudit-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - repoaudit-net
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    # 如果没有 GPU，注释掉上面的 deploy 部分

  # RepoAudit 服务
  repoaudit:
    build: .
    container_name: repoaudit-app
    ports:
      - "8501:8501"
    volumes:
      - ./benchmark:/app/benchmark
      - ./result:/app/result
      - ./src:/app/src
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
    networks:
      - repoaudit-net
    depends_on:
      - ollama
    restart: unless-stopped

volumes:
  ollama_data:

networks:
  repoaudit-net:
    driver: bridge 