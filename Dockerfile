FROM python:3.13-slim

# 设置工作目录
WORKDIR /app

# 更换为国内镜像源（阿里云）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources 2>/dev/null || \
    (echo "deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
     echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
     echo "deb http://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list)

# 安装系统依赖（包括 C++ 编译器）
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    g++ \
    make \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY pyproject.toml .

# 安装 Python 依赖（使用阿里云镜像加速）
RUN pip install --no-cache-dir --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 复制项目文件
COPY . .

# 构建 Tree-sitter 语言绑定（如果失败继续）
RUN cd lib && python build.py 2>&1 || echo "Warning: Tree-sitter build skipped"

# 暴露 Streamlit 端口
EXPOSE 8501

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0

# 创建结果目录
RUN mkdir -p /app/result

# 启动命令（默认启动 Web UI）
CMD ["streamlit", "run", "src/ui/web_ui.py", "--server.address=0.0.0.0"] 