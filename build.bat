@echo off
echo ==========================================
echo RepoAudit + Ollama 部署
echo ==========================================
echo.

echo [1/6] 构建 Docker 镜像...
docker build -t repoaudit:latest .
if errorlevel 1 (
    echo 错误: 镜像构建失败
    pause
    exit /b 1
)
echo 镜像构建成功!
echo.

echo [2/6] 清理旧容器...
docker stop repoaudit-app 2>nul
docker rm repoaudit-app 2>nul
docker-compose -f docker-compose-existing-ollama.yml down 2>nul
echo 清理完成!
echo.

echo [3/6] 启动新容器...
docker-compose -f docker-compose-existing-ollama.yml up -d
if errorlevel 1 (
    echo 错误: 容器启动失败
    pause
    exit /b 1
)
echo 容器启动成功!
echo.

echo [4/6] 等待容器初始化 (15秒)...
timeout /t 15 /nobreak
echo.

echo [5/6] 检查容器状态...
docker ps | findstr repoaudit-app
docker logs --tail 20 repoaudit-app
echo.

echo [6/6] 运行 Bug 检测测试...
echo.
echo 测试 Java NPD 检测 (deepseek-coder:1.3b)...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name deepseek-coder:1.3b --project-path /app/benchmark/Java/toy --bug-type NPD --scan-type dfbscan --call-depth 2 --max-neural-workers 1 --temperature 0.0"

echo.
echo 查看检测结果...
docker exec repoaudit-app find /app/result -name "detect_info.json" -type f
echo.

echo ==========================================
echo 部署完成!
echo ==========================================
echo.
echo Web UI: http://localhost:8501
echo 容器日志: docker logs -f repoaudit-app
echo 进入容器: docker exec -it repoaudit-app bash
echo.
start http://localhost:8501
pause 