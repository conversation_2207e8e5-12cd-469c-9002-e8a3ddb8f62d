@echo off
echo ==========================================
echo 启动并测试 RepoAudit（已修复网络问题）
echo ==========================================
echo.

echo [1/4] 启动容器...
docker-compose -f docker-compose-existing-ollama.yml up -d
timeout /t 15 /nobreak
echo.

echo [2/4] 测试 Java NPD（qwen2.5-coder:7b）...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name qwen2.5-coder:7b --project-path /app/benchmark/Java/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"
echo.

echo [3/4] 测试 Cpp NPD（qwen2.5-coder:7b）...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Cpp --model-name qwen2.5-coder:7b --project-path /app/benchmark/Cpp/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"
echo.

echo [4/4] 查看结果...
docker exec repoaudit-app find /app/result -name "detect_info.json" -type f
echo.

echo ==========================================
echo 完成！打开 Web UI: http://localhost:8501
echo ==========================================
start http://localhost:8501
pause 