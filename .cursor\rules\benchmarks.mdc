---
alwaysApply: true
---
# Benchmark Test Cases

## Purpose
Benchmarks in [benchmark/](mdc:benchmark/) are used for:
- Testing RepoAudit detection capabilities
- Validating new bug type implementations
- Quick start demonstrations
- Regression testing

## Directory Structure
```
benchmark/
├── {Language}/
│   ├── toy/              # Simple test cases
│   │   ├── {BugType}/
│   │   │   ├── case01.{ext}
│   │   │   ├── case02.{ext}
│   │   │   └── ...
│   └── {real-project}/   # Real-world projects (git submodules)
```

## Supported Benchmarks

### C++ ([benchmark/Cpp/](mdc:benchmark/Cpp/))
- **NPD** (Null Pointer Dereference): [toy/NPD/](mdc:benchmark/Cpp/toy/NPD/)
  - [npd-case01.cpp](mdc:benchmark/Cpp/toy/NPD/npd-case01.cpp) through case05
- **MLK** (Memory Leak): [toy/MLK/](mdc:benchmark/Cpp/toy/MLK/)
  - [mlk-case01.cpp](mdc:benchmark/Cpp/toy/MLK/mlk-case01.cpp) through case05
- **UAF** (Use After Free): [toy/UAF/](mdc:benchmark/Cpp/toy/UAF/)
  - [uaf-case01.cpp](mdc:benchmark/Cpp/toy/UAF/uaf-case01.cpp) through case05
- **Real projects**: [sofa-pbrpc/](mdc:benchmark/Cpp/sofa-pbrpc/), [memcached/](mdc:benchmark/C/memcached/), [zstd/](mdc:benchmark/C/zstd/)

### Java ([benchmark/Java/](mdc:benchmark/Java/))
- **NPD**: [toy/NPD/](mdc:benchmark/Java/toy/NPD/)
  - [TestCase1.java](mdc:benchmark/Java/toy/NPD/TestCase1.java) through TestCase5

### Python ([benchmark/Python/](mdc:benchmark/Python/))
- **NPD**: [toy/NPD/](mdc:benchmark/Python/toy/NPD/)
  - [case01.py](mdc:benchmark/Python/toy/NPD/case01.py) through case05

### Go ([benchmark/Go/](mdc:benchmark/Go/))
- **NPD (nil)**: [toy/](mdc:benchmark/Go/toy/)
  - [nil_case01.go](mdc:benchmark/Go/toy/nil_case01.go) through nil_case08
- **BOF**: [toy/](mdc:benchmark/Go/toy/)
  - [bof_case01.go](mdc:benchmark/Go/toy/bof_case01.go) through bof_case07
- **Real projects**: [sally/](mdc:benchmark/Go/sally/)

## Test Case Naming Conventions
- **C/C++**: `{bugtype}-case{NN}.{cpp|c}` (lowercase bug type, hyphenated)
- **Java**: `TestCase{N}.java` (simple numeric)
- **Python**: `case{NN}.py` (simple numeric)
- **Go**: `{bugtype}_case{NN}.go` (lowercase bug type, underscored)

## Writing New Test Cases

### Structure
Each test case should:
1. **Be self-contained**: Minimal dependencies, runnable standalone
2. **Have clear bug**: Obvious vulnerable pattern
3. **Include comments**: Mark bug location with `// BUG:` or `# BUG:`
4. **Test specific scenario**: One primary bug pattern per file

### Example NPD Test Case (C++)
```cpp
// Demonstrates inter-procedural null pointer dereference
int* get_null_value() {
    return nullptr;  // Source: NULL value
}

void use_value(int* ptr) {
    int x = *ptr;  // BUG: Sink - dereference without null check
}

int main() {
    int* value = get_null_value();
    use_value(value);  // Data flow: null → dereference
    return 0;
}
```

### Test Complexity Levels
- **case01-02**: Basic intra-procedural bugs
- **case03-04**: Inter-procedural (function calls)
- **case05+**: Complex control flow (loops, branches, multiple paths)

## Running Benchmarks

### Quick Start Script
Use [run_repoaudit.sh](mdc:src/run_repoaudit.sh) as template:
```bash
cd src
sh run_repoaudit.sh  # Runs DFBScanAgent on Java NPD benchmarks
```

### Manual Execution
```bash
# MetaScan (syntactic analysis only)
python repoaudit.py --scan-type metascan \
  --project-path benchmark/Java/toy/NPD \
  --language Java

# DFBScan (data-flow bug detection)
python repoaudit.py --scan-type dfbscan \
  --project-path benchmark/Cpp/toy/NPD \
  --language Cpp \
  --bug-type NPD \
  --model-name gpt-4 \
  --call-depth 3 \
  --max-neural-workers 5
```

### Results
- **Output**: JSON and log files in current directory
- **Expected**: Should detect bugs marked with `// BUG:` comments
- **Validation**: Check bug reports match expected vulnerabilities

## Git Submodules (Real Projects)
Some benchmarks are external repos (submodules):
```bash
# Initialize submodules
git submodule update --init --recursive

# Examples: memcached, zstd, sofa-pbrpc, sally
```

These test RepoAudit on real-world codebases with complex call graphs and inter-procedural data flows.

## Adding New Benchmarks
1. Create directory: `benchmark/{Language}/toy/{BugType}/`
2. Add test cases: `case01.{ext}`, `case02.{ext}`, etc.
3. Include both buggy and safe variants
4. Document expected behavior in comments
5. Test with corresponding agent and verify detection

## Benchmark Best Practices
- **Diversity**: Cover different code patterns (OOP, functional, procedural)
- **Realism**: Mimic real-world vulnerabilities
- **False positives**: Include safe code that might trigger false alarms
- **Documentation**: Comment the bug scenario and expected detection
