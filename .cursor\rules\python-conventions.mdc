---
globs: *.py
---

# Python Coding Conventions for RepoAudit

## General Patterns

### Class Inheritance
- **Analyzers**: Inherit from base classes (e.g., `TSAnalyzer`, `LLMTool`, `Agent`)
- **Language-specific**: Subclass with naming pattern `{Language}_{BaseClass}` (e.g., `Cpp_TSAnalyzer`)
- **Bug-specific**: Pattern `{Language}_{BugType}_Extractor` (e.g., `Java_NPD_Extractor`)

### Type Hints
- Always use type hints for function parameters and return types
- Import from `typing`: `List`, `Dict`, `Tuple`, `Optional`, etc.
- Example from [repoaudit.py](mdc:src/repoaudit.py):
  ```python
  def traverse_files(self, project_path: str, suffixs: List) -> None:
  ```

### Imports
- Standard library first, then third-party, then local imports
- Use relative imports within package (e.g., `from agent.metascan import *`)
- Common pattern in main files:
  ```python
  import argparse
  import os
  from agent.metascan import *
  from tstool.analyzer.TS_analyzer import *
  ```

## Architecture-Specific Patterns

### TSAnalyzer Subclasses ([tstool/analyzer/](mdc:src/tstool/analyzer/))
- Inherit from `TSAnalyzer` base class
- Override language-specific parsing methods
- Handle file suffixes: Cpp→[cpp,cc,hpp,c,h], Go→[go], Java→[java], Python→[py]

### LLMTool Subclasses ([llmtool/](mdc:src/llmtool/))
- Define `LLMToolInput` and `LLMToolOutput` dataclasses
- Implement prompt loading from [src/prompt/](mdc:src/prompt/)
- Use [LLM_utils.py](mdc:src/llmtool/LLM_utils.py) for LLM invocation

### Agent Subclasses ([agent/](mdc:src/agent/))
- Inherit from base `Agent` class in [agent.py](mdc:src/agent/agent.py)
- Implement `start_scan()` method
- Use both TSTool (parsing) and LLMTool (neural analysis)
- Store state in corresponding State subclass from [memory/semantic/](mdc:src/memory/semantic/)

### Memory Classes ([memory/](mdc:src/memory/))
- **Syntactic**: Store AST-level info (Value, Function, API)
- **Semantic**: Extend `State` base class for agent-specific states
- **Report**: Use `BugReport` for final results

## Error Handling
- Use try-except when reading files (see [repoaudit.py](mdc:src/repoaudit.py) line 161-168)
- Validate inputs before processing (see `validate_inputs()` method)
- Print clear error messages and exit with code 1 on validation failures

## Argparse Configuration
- Define CLI args in dedicated `configure_args()` function
- Required args: `--scan-type`, `--project-path`, `--language`
- Agent-specific args: dfbscan requires `--model-name`, `--bug-type`
- Use `choices` for restricted options (e.g., `choices=["metascan", "dfbscan"]`)

## Parallel Processing
- Use `max_symbolic_workers` for parsing-based analysis (default: 30)
- Use `max_neural_workers` for LLM-based analysis (default: 1)
- Pass worker limits to TSAnalyzer and Agent constructors

## File Traversal
- Exclude common directories: `.git`, `.vscode`, `build`, `dist`, `__pycache__`, `venv`, `target`, etc.
- Filter by language-specific suffixes
- Read files with UTF-8 encoding and `errors='ignore'`

## Supported Languages & Bug Types
Reference `default_dfbscan_checkers` in [repoaudit.py](mdc:src/repoaudit.py):
```python
default_dfbscan_checkers = {
    "Cpp": ["MLK", "NPD", "UAF"],
    "Java": ["NPD"],
    "Python": ["NPD"],
    "Go": ["NPD"],
}
```
 