---
description: Guidelines for adding detection of new bug types to RepoAudit
---

# Adding New Bug Type Support

To add detection for a new bug type (e.g., Buffer Overflow, SQL Injection), follow these steps:

## 1. Classify Bug Type
Determine if it's a **data-flow bug** (DFBScan) or requires a **new agent**:

### Data-Flow Bugs (use DFBScanAgent)
- **Source-must-not-reach-sink**: NPD, Buffer Overflow, SQL Injection
- **Source-must-reach-sink**: Memory Leak, Resource Leak
- Can leverage existing [DFBScanAgent](mdc:src/agent/dfbscan.py)

### Other Bugs (create new agent)
- Logic bugs, concurrency bugs, etc.
- Require new agent class inheriting from [Agent](mdc:src/agent/agent.py)

## 2. Create Extractor for Data-Flow Bugs
**Location**: `src/tstool/dfbscan_extractor/{Language}/{Language}_{BugType}_extractor.py`

Implement source and sink extraction:

```python
class Cpp_BOF_Extractor:  # Buffer Overflow example
    def __init__(self, ts_analyzer):
        self.ts_analyzer = ts_analyzer
    
    def extract_sources(self, function):
        """Identify sources: user inputs, untrusted data"""
        sources = []
        # Parse function AST using ts_analyzer
        # Identify input parameters, file reads, network data, etc.
        return sources
    
    def extract_sinks(self, function):
        """Identify sinks: buffer operations without bounds checking"""
        sinks = []
        # Identify strcpy, memcpy, array access, etc.
        return sinks
    
    def is_sanitizer(self, value):
        """Check if value has been sanitized/validated"""
        # Identify bounds checks, input validation
        return False
```

Reference existing extractors:
- [Cpp_NPD_extractor.py](mdc:src/tstool/dfbscan_extractor/Cpp/Cpp_NPD_extractor.py) - NULL sources and dereference sinks
- [Cpp_MLK_extractor.py](mdc:src/tstool/dfbscan_extractor/Cpp/Cpp_MLK_extractor.py) - Memory allocation sources and free sinks
- [Cpp_UAF_extractor.py](mdc:src/tstool/dfbscan_extractor/Cpp/Cpp_UAF_extractor.py) - Free operations and subsequent use

## 3. Update Extractor Factory
**File**: [dfbscan_extractor.py](mdc:src/tstool/dfbscan_extractor/dfbscan_extractor.py)

Add bug type to extractor mapping:
```python
def get_extractor(language: str, bug_type: str, ts_analyzer):
    if language == "Cpp":
        if bug_type == "NPD":
            return Cpp_NPD_Extractor(ts_analyzer)
        elif bug_type == "MLK":
            return Cpp_MLK_Extractor(ts_analyzer)
        elif bug_type == "BOF":  # Add new bug type
            return Cpp_BOF_Extractor(ts_analyzer)
    # ... other languages
```

## 4. Create LLM Analysis Prompts
**Location**: `src/prompt/{Language}/dfbscan/`

If using DFBScanAgent, prompts should guide LLM on:

### intra_dataflow_analyzer.json
- How to trace data flow for this bug type
- Language-specific patterns (e.g., buffer operations in C++)
- What constitutes a sanitizer/validator

### path_validator.json
- Bug-specific feasibility conditions
- Example: "Can user input reach buffer without size check?"

For new bug type "BOF" in C++:
```json
{
  "system": "You are analyzing C++ code for Buffer Overflow vulnerabilities...",
  "user": "Trace the flow of {source} to {sink}. Check if buffer bounds are validated...",
  "examples": [
    {
      "source": "char* user_input = get_user_input();",
      "sink": "strcpy(buffer, user_input);",
      "analysis": "VULNERABLE: No bounds checking before strcpy..."
    }
  ]
}
```

## 5. Update Default Checkers
**File**: [repoaudit.py](mdc:src/repoaudit.py)

Add to `default_dfbscan_checkers` (line ~16):
```python
default_dfbscan_checkers = {
    "Cpp": ["MLK", "NPD", "UAF", "BOF"],  # Add BOF
    "Java": ["NPD", "SQLi"],  # Example: SQL Injection for Java
    # ...
}
```

## 6. Create Benchmark Test Cases
**Location**: `benchmark/{Language}/toy/{BugType}/`

Create representative test cases:
- **Positive cases**: Contains the bug (true positives)
- **Negative cases**: Similar code without bug (avoid false positives)
- **Edge cases**: Complex control flow, inter-procedural scenarios

Example for BOF:
```cpp
// benchmark/Cpp/toy/BOF/case01.cpp
void vulnerable_function() {
    char buffer[10];
    char* user_input = get_user_input();
    strcpy(buffer, user_input);  // BUG: No bounds check
}

// benchmark/Cpp/toy/BOF/case02.cpp
void safe_function() {
    char buffer[10];
    char* user_input = get_user_input();
    if (strlen(user_input) < 10) {  // Bounds check
        strcpy(buffer, user_input);
    }
}
```

## 7. For Non-Data-Flow Bugs: Create New Agent
If the bug type doesn't fit DFBScan paradigm:

### Create Agent Class
**Location**: `src/agent/{bugtype}_agent.py`

```python
from agent.agent import Agent

class ConcurrencyAgent(Agent):
    def __init__(self, project_path, language, ts_analyzer, ...):
        super().__init__(project_path, language, ts_analyzer)
        # Bug-specific initialization
    
    def start_scan(self):
        # Implement analysis logic
        # Use TSTool for parsing, LLMTool for reasoning
        # Store results in Report memory
        pass
```

### Create Agent State
**Location**: `src/memory/semantic/{bugtype}_state.py`

```python
from memory.semantic.state import State

class ConcurrencyState(State):
    def __init__(self):
        super().__init__()
        self.race_conditions = []
        self.deadlock_scenarios = []
        # Bug-specific state
```

### Create LLM Tools (if needed)
**Location**: `src/llmtool/{bugtype}/`

Implement agent-specific LLM tools following [LLM_tool.py](mdc:src/llmtool/LLM_tool.py) pattern.

## 8. Update Agent Selection
**File**: [repoaudit.py](mdc:src/repoaudit.py)

Add new scan type in `configure_args()` and `start_repo_auditing()`:
```python
parser.add_argument(
    "--scan-type",
    required=True,
    choices=["metascan", "dfbscan", "concurrency"],  # Add new type
    help="The type of scan to perform.",
)

# In start_repo_auditing():
if self.args.scan_type == "concurrency":
    concurrency_agent = ConcurrencyAgent(...)
    concurrency_agent.start_scan()
```

## 9. Testing & Validation
1. **Unit test**: Test extractor on simple code snippets
2. **Benchmark test**: Run on prepared test cases in `benchmark/`
3. **Real-world test**: Test on open-source projects
4. **Precision/Recall**: Measure false positives and false negatives

Example test command:
```bash
python repoaudit.py --scan-type dfbscan \
  --project-path benchmark/Cpp/toy/BOF \
  --language Cpp \
  --bug-type BOF \
  --model-name gpt-4 \
  --call-depth 3
```

## 10. Documentation
- Update [docs/extension.md](mdc:docs/extension.md) with bug type details
- Document bug pattern, sources, sinks, sanitizers
- Add to README supported bug types table
- Include example vulnerabilities and mitigations

## Bug Type Examples

### Data-Flow Bugs (DFBScan)
- **NPD**: NULL → dereference
- **UAF**: free() → use
- **MLK**: malloc → no free
- **BOF**: user_input → buffer_op (no bounds check)
- **SQLi**: user_input → sql_query (no sanitization)
- **XSS**: user_input → html_output (no escaping)

### Other Bug Types (New Agent)
- **Race Condition**: Concurrent access to shared resources
- **Deadlock**: Circular wait on locks
- **Logic Bugs**: Business logic violations
- **API Misuse**: Incorrect usage of library APIs
 