@echo off
echo 启动容器...
docker-compose -f docker-compose-existing-ollama.yml up -d
timeout /t 15 /nobreak

echo.
echo 测试 Java NPD（qwen2.5-coder:7b）...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name qwen2.5-coder:7b --project-path /app/benchmark/Java/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"

echo.
echo 测试 Python NPD（qwen2.5-coder:7b）...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Python --model-name qwen2.5-coder:7b --project-path /app/benchmark/Python/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"

echo.
echo 测试 Cpp NPD（qwen2.5-coder:7b）...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Cpp --model-name qwen2.5-coder:7b --project-path /app/benchmark/Cpp/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"

echo.
echo 查看结果...
docker exec repoaudit-app find /app/result -name "detect_info.json" -type f

echo.
echo 打开 Web UI...
start http://localhost:8501
pause 