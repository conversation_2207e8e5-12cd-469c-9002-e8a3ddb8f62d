@echo off
echo ==========================================
echo 修复并完整部署 RepoAudit
echo ==========================================
echo.

echo [1/5] 清理旧容器...
docker stop repoaudit-app 2>nul
docker rm repoaudit-app 2>nul
docker-compose -f docker-compose-existing-ollama.yml down 2>nul
echo.

echo [2/5] 重新构建镜像（使用国内镜像源）...
docker build -t repoaudit:latest . --no-cache
if errorlevel 1 (
    echo 错误: 镜像构建失败
    pause
    exit /b 1
)
echo 镜像构建成功！
echo.

echo [3/5] 启动容器...
docker-compose -f docker-compose-existing-ollama.yml up -d
timeout /t 15 /nobreak
echo.

echo [4/5] 运行多语言测试（qwen2.5-coder:7b）...
echo.
echo 测试 Java NPD...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Java --model-name qwen2.5-coder:7b --project-path /app/benchmark/Java/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"

echo.
echo 测试 Cpp NPD...
docker exec repoaudit-app bash -c "cd /app/src && python repoaudit.py --language Cpp --model-name qwen2.5-coder:7b --project-path /app/benchmark/Cpp/toy/NPD --bug-type NPD --scan-type dfbscan --call-depth 3 --max-neural-workers 2 --temperature 0.0"

echo.
echo [5/5] 查看结果...
docker exec repoaudit-app find /app/result -name "detect_info.json" -type f
echo.

echo ==========================================
echo 部署完成！
echo ==========================================
echo Web UI: http://localhost:8501
echo 容器日志: docker logs -f repoaudit-app
echo 进入容器: docker exec -it repoaudit-app bash
echo.
start http://localhost:8501
pause 