---
alwaysApply: true
---
# Adding New Language Support

To add support for a new programming language (e.g., Rust, JavaScript), follow these steps:

## 1. Tree-sitter Language Binding
- Add the language's tree-sitter grammar to [lib/](mdc:lib/) directory
- Update `build.py` to compile the new language grammar
- Ensure language-specific AST node types are documented

## 2. Create TSAnalyzer Subclass
**Location**: `src/tstool/analyzer/{Language}_TS_analyzer.py`

- Inherit from [TS_analyzer.py](mdc:src/tstool/analyzer/TS_analyzer.py) base class
- Implement language-specific AST parsing logic
- Extract: functions, values (params/returns/args), control flow, call graph
- Define file suffixes for the language
- Reference existing analyzers:
  - [Cpp_TS_analyzer.py](mdc:src/tstool/analyzer/Cpp_TS_analyzer.py)
  - [Java_TS_analyzer.py](mdc:src/tstool/analyzer/Java_TS_analyzer.py)
  - [Python_TS_analyzer.py](mdc:src/tstool/analyzer/Python_TS_analyzer.py)

Example skeleton:
```python
from tstool.analyzer.TS_analyzer import TSAnalyzer

class Rust_TSAnalyzer(TSAnalyzer):
    def __init__(self, code_in_files, language, max_workers):
        super().__init__(code_in_files, language, max_workers)
        # Language-specific initialization
```

## 3. Create Bug-Specific Extractors
**Location**: `src/tstool/dfbscan_extractor/{Language}/`

For each bug type (NPD, MLK, UAF):
- Create `{Language}_{BugType}_extractor.py`
- Implement source/sink identification logic
- Example: [Cpp_NPD_extractor.py](mdc:src/tstool/dfbscan_extractor/Cpp/Cpp_NPD_extractor.py)

Pattern:
```python
class Rust_NPD_Extractor:
    def extract_sources(self, function):
        # Identify NULL/None equivalent values
        pass
    
    def extract_sinks(self, function):
        # Identify dereference operations
        pass
```

## 4. Create LLM Prompts
**Location**: `src/prompt/{Language}/dfbscan/`

Create JSON prompt templates:
- `intra_dataflow_analyzer.json` - For data-flow fact extraction
- `path_validator.json` - For path feasibility validation

Reference templates from other languages:
- [Python/dfbscan/](mdc:src/prompt/Python/dfbscan/)
- [Java/dfbscan/](mdc:src/prompt/Java/dfbscan/)

Customize prompts with language-specific:
- Syntax examples
- Null/None semantics
- Memory management patterns
- Common idioms

## 5. Update Main Entry Point
**File**: [repoaudit.py](mdc:src/repoaudit.py)

Add language to supported list:

1. Import the new analyzer (line ~10):
   ```python
   from tstool.analyzer.Rust_TS_analyzer import *
   ```

2. Update `default_dfbscan_checkers` (line ~16):
   ```python
   default_dfbscan_checkers = {
       # ... existing ...
       "Rust": ["NPD"],  # Add supported bug types
   }
   ```

3. Add file suffix mapping (line ~53-63):
   ```python
   elif self.language == "Rust":
       suffixs = ["rs"]
   ```

4. Add analyzer initialization (line ~68-84):
   ```python
   elif self.language == "Rust":
       self.ts_analyzer = Rust_TSAnalyzer(
           self.code_in_files, self.language, self.max_symbolic_workers
       )
   ```

## 6. Create Benchmark Test Cases
**Location**: `benchmark/{Language}/toy/`

Create subdirectories for each bug type:
- `benchmark/Rust/toy/NPD/` - Test cases for NPD bugs
- Include both positive (buggy) and negative (clean) examples
- Follow naming: `case01.rs`, `case02.rs`, etc.

Reference existing benchmarks:
- [benchmark/Java/toy/NPD/](mdc:benchmark/Java/toy/NPD/)
- [benchmark/Python/toy/NPD/](mdc:benchmark/Python/toy/NPD/)

## 7. Testing
1. Test parsing: Run MetaScanAgent to verify AST extraction
   ```bash
   python repoaudit.py --scan-type metascan --project-path benchmark/Rust/toy --language Rust
   ```

2. Test bug detection: Run DFBScanAgent on benchmark
   ```bash
   python repoaudit.py --scan-type dfbscan --project-path benchmark/Rust/toy/NPD \
     --language Rust --bug-type NPD --model-name gpt-4
   ```

3. Verify results in generated JSON and log files

## 8. Documentation
- Update [docs/extension.md](mdc:docs/extension.md) with language-specific notes
- Add language to README supported languages table
- Document any language-specific limitations or caveats

## Key Considerations
- **Null semantics**: How does the language represent null/nil/None?
- **Memory model**: Manual (C/C++/Rust) vs. GC (Java/Python/Go)?
- **Type system**: Static vs. dynamic typing affects analysis precision
- **Concurrency**: Language-specific concurrency primitives may require special handling
